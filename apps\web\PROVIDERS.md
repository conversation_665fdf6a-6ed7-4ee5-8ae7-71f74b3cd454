# Providers Setup Documentation

This document explains the comprehensive provider setup for the O'Local web application.

## Overview

The `providers.tsx` file sets up multiple providers that wrap the entire application:

1. **ErrorBoundary** - Catches and handles React errors gracefully
2. **ThemeProvider** - Manages light/dark theme state
3. **QueryClientProvider** - Provides React Query functionality
4. **SessionProvider** - Provides NextAuth session management
5. **AuthProvider** - Custom authentication context
6. **Toaster** - Global toast notifications
7. **ReactQueryDevtools** - Development tools for React Query

## Providers Breakdown

### 1. ErrorBoundary
- Catches JavaScript errors anywhere in the component tree
- Displays a fallback UI when errors occur
- Provides a "Refresh Page" button for recovery
- Logs errors to console for debugging

### 2. ThemeProvider
- Manages theme state (light, dark, system)
- Persists theme preference in localStorage
- Automatically applies system theme when "system" is selected
- Provides `useTheme()` hook for components

### 3. AuthProvider
- Custom authentication context using the backend API
- Manages user state and authentication tokens
- Provides login/logout functionality
- Automatically validates existing sessions on app load
- Provides `useAuth()` hook for components

### 4. React Query Setup
- Configured with sensible defaults:
  - 1-minute stale time for queries
  - Smart retry logic (no retry on 4xx errors)
  - No retry for mutations
- Includes devtools for development

### 5. Toast Notifications
- Global toast system using react-hot-toast
- Configured with custom styling
- Different styles for success/error messages
- Positioned at top-right

## Usage Examples

### Using Authentication
```tsx
import { useAuth } from '@/app/providers'

function MyComponent() {
  const { user, login, logout, loading } = useAuth()
  
  if (loading) return <div>Loading...</div>
  
  if (!user) {
    return <button onClick={() => login(email, password)}>Login</button>
  }
  
  return (
    <div>
      Welcome, {user.name}!
      <button onClick={logout}>Logout</button>
    </div>
  )
}
```

### Using Theme
```tsx
import { useTheme } from '@/app/providers'

function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  
  return (
    <select value={theme} onChange={(e) => setTheme(e.target.value)}>
      <option value="light">Light</option>
      <option value="dark">Dark</option>
      <option value="system">System</option>
    </select>
  )
}
```

### Using API with React Query
```tsx
import { useApi } from '@/hooks/useApi'

function UserProfile() {
  const { useApiQuery, useApiMutation } = useApi()
  
  const { data: user, isLoading } = useApiQuery(
    ['user', 'profile'],
    '/user/profile'
  )
  
  const updateProfile = useApiMutation(
    '/user/profile',
    'PUT',
    {
      successMessage: 'Profile updated!',
      invalidateQueries: [['user', 'profile']]
    }
  )
  
  if (isLoading) return <div>Loading...</div>
  
  return (
    <div>
      <h1>{user?.name}</h1>
      <button onClick={() => updateProfile.mutate({ name: 'New Name' })}>
        Update Profile
      </button>
    </div>
  )
}
```

## Environment Variables

Make sure to set up the following environment variables:

```env
# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# API Configuration
NEXT_PUBLIC_API_URL="http://localhost:3001"

# App Configuration
NEXT_PUBLIC_APP_NAME="O'Local"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

## Dependencies

The following packages are required:

- `@tanstack/react-query` - Data fetching and caching
- `@tanstack/react-query-devtools` - Development tools
- `next-auth` - Authentication
- `react-hot-toast` - Toast notifications

## Features

### Error Handling
- Global error boundary catches unhandled errors
- Graceful fallback UI with recovery option
- Error logging for debugging

### Authentication
- JWT-based authentication with the backend
- Automatic token management
- Session persistence across page reloads
- Logout on token expiration

### Theme Management
- Support for light, dark, and system themes
- Automatic system theme detection
- Persistent theme preferences

### Data Management
- Optimistic updates with React Query
- Intelligent caching and background refetching
- Error handling with toast notifications
- Development tools for debugging

### User Experience
- Loading states for all async operations
- Success/error feedback via toasts
- Responsive error handling
- Smooth theme transitions

## Best Practices

1. **Always use the provided hooks** (`useAuth`, `useTheme`, `useApi`) instead of accessing contexts directly
2. **Handle loading and error states** in your components
3. **Use the API hook** for consistent error handling and toast notifications
4. **Leverage React Query's caching** by using consistent query keys
5. **Test error scenarios** to ensure the error boundary works correctly

## Troubleshooting

### Common Issues

1. **"Cannot find module" errors**: Make sure all dependencies are installed
2. **Authentication not working**: Check API URL and ensure backend is running
3. **Theme not persisting**: Check localStorage permissions
4. **Toasts not showing**: Ensure Toaster component is rendered

### Debug Tools

- React Query Devtools (available in development)
- Browser console for error logs
- Network tab for API request debugging
- localStorage inspection for tokens and theme
