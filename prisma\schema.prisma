generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  phone     String?  @unique
  name      String
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  bookings     Booking[]
  reviews      Review[]
  provider     Provider?
  refreshToken RefreshToken[]

  @@map("users")
}

model Provider {
  id          String   @id @default(cuid())
  userId      String   @unique
  businessName String
  description String?
  location    String
  latitude    Float?
  longitude   Float?
  verified    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  services Service[]
  bookings Booking[]
  reviews  Review[]

  @@map("providers")
}

model Service {
  id          String   @id @default(cuid())
  providerId  String
  name        String
  description String?
  price       Float
  duration    Int      // minutes
  category    Category
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  provider Provider  @relation(fields: [providerId], references: [id], onDelete: Cascade)
  bookings Booking[]

  @@map("services")
}

model Booking {
  id         String        @id @default(cuid())
  userId     String
  providerId String
  serviceId  String
  startTime  DateTime
  endTime    DateTime
  status     BookingStatus @default(PENDING)
  totalPrice Float
  notes      String?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  // Relations
  user     User     @relation(fields: [userId], references: [id])
  provider Provider @relation(fields: [providerId], references: [id])
  service  Service  @relation(fields: [serviceId], references: [id])
  payment  Payment?
  review   Review?

  @@map("bookings")
}

model Payment {
  id        String        @id @default(cuid())
  bookingId String        @unique
  amount    Float
  status    PaymentStatus @default(PENDING)
  method    PaymentMethod
  stripeId  String?
  mpesaId   String?
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relations
  booking Booking @relation(fields: [bookingId], references: [id])

  @@map("payments")
}

model Review {
  id         String   @id @default(cuid())
  userId     String
  providerId String
  bookingId  String   @unique
  rating     Int      @db.SmallInt
  comment    String?
  createdAt  DateTime @default(now())

  // Relations
  user     User     @relation(fields: [userId], references: [id])
  provider Provider @relation(fields: [providerId], references: [id])
  booking  Booking  @relation(fields: [bookingId], references: [id])

  @@map("reviews")
}

model RefreshToken {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

enum Role {
  USER
  PROVIDER
  ADMIN
}

enum Category {
  BEAUTY
  TUTORING
  HANDYMAN
  CLEANING
  TECH_REPAIR
  FITNESS
  OTHER
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum PaymentMethod {
  STRIPE
  MPESA
  CASH
}