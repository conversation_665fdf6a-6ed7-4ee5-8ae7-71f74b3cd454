{"name": "@olocal/api", "version": "0.1.0", "scripts": {"build": "nest build", "dev": "nest start --watch", "start": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\""}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@prisma/client": "^5.6.0", "@olocal/common": "*", "bcrypt": "^5.1.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "helmet": "^7.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/passport-jwt": "^3.0.13", "prisma": "^5.6.0", "typescript": "^5.0.0"}}