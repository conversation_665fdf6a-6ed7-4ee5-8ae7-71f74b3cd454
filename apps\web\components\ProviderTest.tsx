'use client'

import { useAuth, useTheme } from '@/app/providers'
import { useApi } from '@/hooks/useApi'
import { Button } from '@olocal/ui'
import toast from 'react-hot-toast'

export function ProviderTest() {
  const { user, loading, logout } = useAuth()
  const { theme, setTheme } = useTheme()
  const { useApiQuery } = useApi()

  // Test API query (this will fail gracefully if backend isn't running)
  const { data: testData, isLoading: apiLoading, error } = useApiQuery(
    ['test'],
    '/health',
    { requireAuth: false, enabled: true }
  )

  const handleToastTest = () => {
    toast.success('Toast notification working!')
  }

  const handleErrorTest = () => {
    throw new Error('Test error for error boundary')
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
        Provider Test Component
      </h2>
      
      {/* Authentication Status */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-2">Authentication Status</h3>
        {loading ? (
          <p>Loading authentication...</p>
        ) : user ? (
          <div>
            <p className="text-green-600">✅ Authenticated as: {user.name}</p>
            <p className="text-sm text-gray-600">Email: {user.email}</p>
            <p className="text-sm text-gray-600">Role: {user.role}</p>
            <Button onClick={logout} variant="outline" className="mt-2">
              Logout
            </Button>
          </div>
        ) : (
          <p className="text-red-600">❌ Not authenticated</p>
        )}
      </div>

      {/* Theme Status */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-2">Theme Management</h3>
        <p className="mb-2">Current theme: <span className="font-mono">{theme}</span></p>
        <div className="space-x-2">
          <Button 
            onClick={() => setTheme('light')} 
            variant={theme === 'light' ? 'default' : 'outline'}
            size="sm"
          >
            Light
          </Button>
          <Button 
            onClick={() => setTheme('dark')} 
            variant={theme === 'dark' ? 'default' : 'outline'}
            size="sm"
          >
            Dark
          </Button>
          <Button 
            onClick={() => setTheme('system')} 
            variant={theme === 'system' ? 'default' : 'outline'}
            size="sm"
          >
            System
          </Button>
        </div>
      </div>

      {/* API Status */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-2">API Connection</h3>
        {apiLoading ? (
          <p>Testing API connection...</p>
        ) : error ? (
          <p className="text-red-600">❌ API connection failed: {error.message}</p>
        ) : testData ? (
          <p className="text-green-600">✅ API connection successful</p>
        ) : (
          <p className="text-yellow-600">⚠️ API test skipped</p>
        )}
      </div>

      {/* Toast Test */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-2">Toast Notifications</h3>
        <div className="space-x-2">
          <Button onClick={handleToastTest} variant="outline">
            Test Success Toast
          </Button>
          <Button onClick={() => toast.error('Error toast test!')} variant="outline">
            Test Error Toast
          </Button>
        </div>
      </div>

      {/* Error Boundary Test */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-2">Error Boundary</h3>
        <p className="text-sm text-gray-600 mb-2">
          Click the button below to test the error boundary (this will crash this component)
        </p>
        <Button onClick={handleErrorTest} variant="destructive">
          Test Error Boundary
        </Button>
      </div>

      {/* React Query Devtools Info */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-2">Development Tools</h3>
        <p className="text-sm text-gray-600">
          React Query Devtools are available in development mode. 
          Look for the React Query icon in the bottom corner of your screen.
        </p>
      </div>
    </div>
  )
}
