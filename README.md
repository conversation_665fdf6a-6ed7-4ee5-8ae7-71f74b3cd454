# O'Local - Hyperlocal Services Platform

A modern full-stack platform for discovering and booking local service providers.

## 🚀 Quick Start

1. **Clone and setup:**
   ```bash
   git clone <your-repo>
   cd olocal
   npm run setup
   ```

2. **Environment setup:**
   ```bash
   cp .env.example .env
   # Edit .env with your database URL and secrets
   ```

3. **Start development:**
   ```bash
   npm run dev
   ```

4. **Access the apps:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Database Studio: `npm run db:studio`

## 📁 Project Structure

```
olocal/
├── apps/
│   ├── web/          # Next.js frontend
│   └── api/          # NestJS backend
├── packages/
│   ├── ui/           # Shared UI components
│   └── common/       # Shared types & utilities
├── prisma/           # Database schema
└── scripts/          # Setup & deployment scripts
```

## 🛠️ Tech Stack

- **Frontend:** Next.js 14, Tailwind CSS, TypeScript
- **Backend:** NestJS, Prisma, PostgreSQL
- **Auth:** JWT with refresh tokens
- **Payments:** Stripe, M-Pesa ready
- **Deployment:** Vercel + Render/Fly.io

## 📋 Development Tasks

### Phase 1: Foundation ✅
- [x] Monorepo setup with Turborepo
- [x] Database schema with Prisma
- [x] JWT authentication system
- [x] Basic UI components

### Phase 2: Core Features (Current)
- [ ] Provider registration & verification
- [ ] Service CRUD operations
- [ ] Location-based search
- [ ] Booking system

### Phase 3: Advanced Features
- [ ] Payment integration
- [ ] Reviews & ratings
- [ ] Real-time notifications
- [ ] Mobile app (React Native)

## 🚀 Deployment

- **Frontend:** Auto-deploy to Vercel on push to main
- **Backend:** Deploy to Render or Fly.io
- **Database:** Neon PostgreSQL (free tier)

## 📖 API Documentation

Once running, visit http://localhost:3001/api for Swagger docs.