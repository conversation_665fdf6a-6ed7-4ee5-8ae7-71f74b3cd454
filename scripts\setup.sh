#!/bin/bash

echo "🚀 Setting up O'Local development environment..."

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Setup database
echo "🗄️ Setting up database..."
docker-compose up -d postgres

# Wait for postgres to be ready
echo "⏳ Waiting for database to be ready..."
sleep 5

# Run migrations
echo "🔄 Running database migrations..."
cd apps/api && npx prisma migrate dev --name init

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Seed database (optional)
echo "🌱 Seeding database..."
npx prisma db seed

echo "✅ Setup complete! Run 'npm run dev' to start development servers."