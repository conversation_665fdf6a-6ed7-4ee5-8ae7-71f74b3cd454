import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    accessToken?: string
    refreshToken?: string
    user: {
      id: string
      email: string
      name: string
      role: 'USER' | 'PROVIDER' | 'ADMIN'
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: 'USER' | 'PROVIDER' | 'ADMIN'
    accessToken?: string
    refreshToken?: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken?: string
    refreshToken?: string
    role?: 'USER' | 'PROVIDER' | 'ADMIN'
  }
}
