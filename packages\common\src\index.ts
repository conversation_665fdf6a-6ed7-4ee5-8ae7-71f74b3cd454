// Common types and utilities for O'Local

export interface User {
  id: string
  email: string
  name: string
  phone?: string
  role: 'USER' | 'PROVIDER' | 'ADMIN'
  createdAt: Date
  updatedAt: Date
}

export interface Provider {
  id: string
  userId: string
  businessName: string
  description?: string
  verified: boolean
  rating: number
  totalReviews: number
  createdAt: Date
  updatedAt: Date
}

export interface Service {
  id: string
  providerId: string
  title: string
  description: string
  price: number
  duration: number
  category: string
  location: string
  available: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Booking {
  id: string
  userId: string
  serviceId: string
  providerId: string
  scheduledAt: Date
  status: 'PENDING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED'
  totalAmount: number
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export interface Review {
  id: string
  userId: string
  providerId: string
  bookingId: string
  rating: number
  comment?: string
  createdAt: Date
  updatedAt: Date
}

// API Response types
export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Utility functions
export function formatCurrency(amount: number, currency = 'KES'): string {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency,
  }).format(amount)
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('en-KE', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(d)
}

export function formatTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('en-KE', {
    hour: '2-digit',
    minute: '2-digit',
  }).format(d)
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-')
}

// Validation helpers
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPhone(phone: string): boolean {
  // Kenyan phone number validation
  const phoneRegex = /^(\+254|0)[17]\d{8}$/
  return phoneRegex.test(phone)
}

// Constants
export const ROLES = {
  USER: 'USER',
  PROVIDER: 'PROVIDER',
  ADMIN: 'ADMIN',
} as const

export const BOOKING_STATUS = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
} as const

export const SERVICE_CATEGORIES = [
  'Beauty & Wellness',
  'Home Services',
  'Tutoring & Education',
  'Health & Fitness',
  'Technology',
  'Events & Entertainment',
  'Transportation',
  'Other',
] as const
