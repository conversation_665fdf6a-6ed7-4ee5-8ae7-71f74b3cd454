import { Button } from '@olocal/ui'
import Link from 'next/link'
import { ProviderTest } from '@/components/ProviderTest'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Find Local Services
            <span className="text-blue-600"> Near You</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Connect with trusted local service providers. From beauty services to tutoring, 
            find and book the perfect professional for your needs.
          </p>
          
          <div className="flex gap-4 justify-center">
            <Link href="/services">
              <Button size="lg">Find Services</Button>
            </Link>
            <Link href="/provider/register">
              <Button variant="outline" size="lg">Become a Provider</Button>
            </Link>
          </div>
        </div>

        <div className="mt-16 grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md">
              <div className="text-blue-600 mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Provider Test Section */}
      <div className="container mx-auto px-4 py-16">
        <ProviderTest />
      </div>
    </div>
  )
}

const features = [
  {
    icon: "🔍",
    title: "Easy Discovery",
    description: "Find local service providers based on your location and needs"
  },
  {
    icon: "⭐",
    title: "Verified Reviews",
    description: "Read authentic reviews from real customers to make informed decisions"
  },
  {
    icon: "💳",
    title: "Secure Payments",
    description: "Pay safely with multiple payment options including M-Pesa and Stripe"
  }
]