#!/usr/bin/env node

/**
 * Simple test script to verify provider setup
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Testing O\'Local Provider Setup...\n')

const checks = [
  {
    name: 'Providers file exists',
    check: () => fs.existsSync(path.join(__dirname, '../app/providers.tsx')),
  },
  {
    name: 'NextAuth route exists',
    check: () => fs.existsSync(path.join(__dirname, '../app/api/auth/[...nextauth]/route.ts')),
  },
  {
    name: 'TypeScript config exists',
    check: () => fs.existsSync(path.join(__dirname, '../tsconfig.json')),
  },
  {
    name: 'Environment example exists',
    check: () => fs.existsSync(path.join(__dirname, '../.env.example')),
  },
  {
    name: 'API hook exists',
    check: () => fs.existsSync(path.join(__dirname, '../hooks/useApi.ts')),
  },
  {
    name: 'Test component exists',
    check: () => fs.existsSync(path.join(__dirname, '../components/ProviderTest.tsx')),
  },
  {
    name: 'Global styles exist',
    check: () => fs.existsSync(path.join(__dirname, '../app/globals.css')),
  },
  {
    name: 'NextAuth types exist',
    check: () => fs.existsSync(path.join(__dirname, '../types/next-auth.d.ts')),
  },
]

let passed = 0
let failed = 0

checks.forEach(({ name, check }) => {
  const result = check()
  if (result) {
    console.log(`✅ ${name}`)
    passed++
  } else {
    console.log(`❌ ${name}`)
    failed++
  }
})

console.log(`\n📊 Results: ${passed} passed, ${failed} failed`)

if (failed === 0) {
  console.log('\n🎉 All provider setup checks passed!')
  console.log('\n📋 Next steps:')
  console.log('1. Copy .env.example to .env.local and fill in your values')
  console.log('2. Start the development server: npm run dev')
  console.log('3. Visit http://localhost:3000 to see the provider test component')
  console.log('4. Check the browser console for any runtime errors')
} else {
  console.log('\n⚠️  Some checks failed. Please review the setup.')
  process.exit(1)
}
