import { useAuth } from '@/app/providers'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL

interface ApiOptions {
  requireAuth?: boolean
  showSuccessToast?: boolean
  showErrorToast?: boolean
  successMessage?: string
}

export function useApi() {
  const { user, logout } = useAuth()
  const queryClient = useQueryClient()

  const makeRequest = async (
    endpoint: string,
    options: RequestInit = {},
    apiOptions: ApiOptions = {}
  ) => {
    const { requireAuth = true } = apiOptions
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (requireAuth && user) {
      const token = localStorage.getItem('accessToken')
      if (token) {
        headers.Authorization = `Bearer ${token}`
      }
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers,
    })

    if (response.status === 401 && requireAuth) {
      logout()
      throw new Error('Unauthorized')
    }

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'An error occurred' }))
      throw new Error(error.message || 'An error occurred')
    }

    return response.json()
  }

  const useApiQuery = <T>(
    key: string[],
    endpoint: string,
    options: ApiOptions & { enabled?: boolean } = {}
  ) => {
    return useQuery<T>({
      queryKey: key,
      queryFn: () => makeRequest(endpoint, {}, options),
      enabled: options.enabled !== false && (options.requireAuth ? !!user : true),
    })
  }

  const useApiMutation = <T, V>(
    endpoint: string,
    method: 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'POST',
    options: ApiOptions & {
      onSuccess?: (data: T) => void
      invalidateQueries?: string[][]
    } = {}
  ) => {
    return useMutation<T, Error, V>({
      mutationFn: (data: V) =>
        makeRequest(
          endpoint,
          {
            method,
            body: JSON.stringify(data),
          },
          options
        ),
      onSuccess: (data) => {
        if (options.showSuccessToast !== false) {
          toast.success(options.successMessage || 'Success!')
        }
        
        if (options.invalidateQueries) {
          options.invalidateQueries.forEach((queryKey) => {
            queryClient.invalidateQueries({ queryKey })
          })
        }
        
        options.onSuccess?.(data)
      },
      onError: (error) => {
        if (options.showErrorToast !== false) {
          toast.error(error.message || 'An error occurred')
        }
      },
    })
  }

  return {
    makeRequest,
    useApiQuery,
    useApiMutation,
  }
}
